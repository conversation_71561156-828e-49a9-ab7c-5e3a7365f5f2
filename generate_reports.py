#!/usr/bin/env python3
"""
Simple script to generate reports from existing output files
"""
import json
import os
from datetime import datetime

def load_json_file(filepath):
    """Load JSON data from file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {filepath}: {e}")
        return None

def load_text_file(filepath):
    """Load text data from file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"Error loading {filepath}: {e}")
        return None

def generate_markdown_report():
    """Generate comprehensive markdown report"""

    # Load existing data
    normalized_data = load_json_file("outputs/final/normalized_output.json")
    arima_data = load_json_file("outputs/predictions/arima_output.json")
    classification_data = load_json_file("outputs/predictions/classification_output.json")
    generated_text = load_text_file("outputs/final/generated_text_bert.txt")

    # Start building the markdown report
    report = []
    report.append("# 📊 Business Intelligence & Predictive Analytics Report")
    report.append(f"**Generated on:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    report.append("## 🎯 Project Overview")
    report.append("This report presents a comprehensive business analysis using machine learning models to:")
    report.append("- **Predict sales trends** using ARIMA time series forecasting")
    report.append("- **Classify business owner willingness** to adopt new technologies using Random Forest")
    report.append("- **Generate actionable insights** for business development strategies")
    report.append("")

    # Executive Summary
    report.append("## 📋 Executive Summary")
    if normalized_data:
        store_id = normalized_data.get('store', 'N/A')
        trend = normalized_data.get('trend', 'unknown')
        prediction = normalized_data.get('prediction', 'unknown')

        report.append(f"This comprehensive analysis focuses on **Store {store_id}** and provides:")
        report.append("")
        report.append("### 🔍 Key Findings")

        # Determine trend emoji and description
        trend_emoji = "📉" if 'turun' in trend.lower() or 'down' in trend.lower() else "📈"
        trend_desc = "declining" if 'turun' in trend.lower() else "stable/growing"

        # Determine willingness emoji
        willing_emoji = "✅" if 'bersedia' in prediction.lower() or 'willing' in prediction.lower() else "❌"

        report.append(f"- {trend_emoji} **Sales Trend:** {trend.upper()} ({trend_desc} sales pattern)")
        report.append(f"- {willing_emoji} **Technology Adoption:** {prediction} (business owner's willingness to develop)")

        # Add urgency level based on trend
        if 'turun' in trend.lower():
            report.append("- 🚨 **Urgency Level:** HIGH - Immediate action required")
        else:
            report.append("- ✅ **Urgency Level:** MODERATE - Continue monitoring")

        report.append("")

    # Sales Forecast Analysis (ARIMA)
    report.append("## 📈 Sales Forecast Analysis (ARIMA Time Series Model)")
    if arima_data:
        report.append("### 🎯 3-Day Sales Forecast")
        forecast = arima_data.get('forecast', [])
        forecast_dates = arima_data.get('forecast_dates', [])

        if forecast and forecast_dates:
            report.append("| 📅 Date | 💰 Predicted Sales (Rp) | 📊 Daily Change |")
            report.append("|---------|-------------------------|------------------|")

            for i, (date, value) in enumerate(zip(forecast_dates, forecast)):
                if i == 0:
                    change = "Baseline"
                else:
                    prev_value = forecast[i-1]
                    change_amount = value - prev_value
                    change_percent = (change_amount / prev_value) * 100
                    change_emoji = "📈" if change_amount > 0 else "📉"
                    change = f"{change_emoji} {change_amount:+,.0f} ({change_percent:+.1f}%)"

                report.append(f"| {date} | Rp {value:,.2f} | {change} |")

        report.append("")
        report.append("### 📊 Trend Analysis")
        trend = arima_data.get('trend', 'unknown')
        store_id = arima_data.get('store', 'N/A')

        if 'turun' in trend.lower():
            report.append(f"🚨 **Critical Alert:** Store {store_id} shows a **declining sales trend**")
            report.append("- **Impact:** Revenue reduction over the forecast period")
            report.append("- **Risk Level:** HIGH")
            report.append("- **Action Required:** Immediate intervention needed")
        else:
            report.append(f"✅ **Status:** Store {store_id} shows a **{trend}** sales trend")
            report.append("- **Impact:** Stable/positive revenue trajectory")
            report.append("- **Risk Level:** LOW to MODERATE")

        report.append("")

    # Classification Analysis
    report.append("## 🤖 Business Owner Technology Adoption Analysis")
    if classification_data:
        report.append("### 📊 Model Performance Metrics")
        accuracy = classification_data.get('accuracy', 'N/A')
        if isinstance(accuracy, float):
            accuracy_percent = accuracy * 100
            performance_emoji = "🎯" if accuracy > 0.7 else "⚠️" if accuracy > 0.5 else "❌"
            report.append(f"- {performance_emoji} **Model Accuracy:** {accuracy_percent:.1f}% ({accuracy:.4f})")

            if accuracy > 0.7:
                report.append("- **Performance Level:** HIGH - Reliable predictions")
            elif accuracy > 0.5:
                report.append("- **Performance Level:** MODERATE - Use with caution")
            else:
                report.append("- **Performance Level:** LOW - Consider model improvement")
        else:
            report.append(f"- **Model Accuracy:** {accuracy}")

        report.append("")
        report.append("### 🎯 Classification Results")
        prediction = classification_data.get('prediksi', classification_data.get('prediction', 'unknown'))

        if 'bersedia' in prediction.lower() or 'willing' in prediction.lower():
            report.append("✅ **Business Owner Classification: WILLING TO ADOPT TECHNOLOGY**")
            report.append("- **Implication:** High potential for digital transformation")
            report.append("- **Opportunity:** Ready for technology investments")
            report.append("- **Strategy:** Accelerate digital initiatives")
        else:
            report.append("❌ **Business Owner Classification: RELUCTANT TO ADOPT TECHNOLOGY**")
            report.append("- **Implication:** Resistance to digital transformation")
            report.append("- **Challenge:** Need change management approach")
            report.append("- **Strategy:** Gradual technology introduction")

        # Top features analysis
        if 'top_features' in classification_data:
            report.append("")
            report.append("### 🔍 Key Decision Factors")
            report.append("The following factors most strongly influence technology adoption willingness:")
            report.append("")

            features = classification_data['top_features']
            for i, feature in enumerate(features, 1):
                emoji = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else "📊"
                report.append(f"{emoji} **{i}. {feature}**")

            report.append("")
            report.append("**Analysis Insights:**")
            if 'E-Wallet Acceptance' in features:
                report.append("- Digital payment adoption is a strong predictor")
            if 'Has Website' in features:
                report.append("- Online presence indicates technology readiness")
            if 'Marketplace Usage' in features:
                report.append("- E-commerce experience influences willingness")

        report.append("")

    # Detailed Forecast
    report.append("## Detailed Sales Forecast")
    if normalized_data:
        forecast = normalized_data.get('forecast', [])
        forecast_dates = normalized_data.get('forecast_dates', [])

        if forecast and forecast_dates:
            report.append("### 3-Day Sales Projection")
            for i, (date, value) in enumerate(zip(forecast_dates, forecast), 1):
                report.append(f"**Day {i} ({date}):** Rp {value:,.2f}")

            # Calculate trend
            if len(forecast) >= 2:
                trend_direction = "increasing" if forecast[-1] > forecast[0] else "decreasing"
                total_change = forecast[-1] - forecast[0]
                report.append(f"\n**Overall Trend:** {trend_direction} by Rp {abs(total_change):,.2f}")

        report.append("")

    # Generated Narrative
    if generated_text:
        report.append("## AI-Generated Business Narrative")
        report.append("```")
        report.append(generated_text)
        report.append("```")
        report.append("")

    # Recommendations
    report.append("## Strategic Recommendations")
    if normalized_data:
        trend = normalized_data.get('trend', '').lower()
        prediction = normalized_data.get('prediction', '').lower()

        if 'turun' in trend or 'decreasing' in trend:
            report.append("### Immediate Actions Required")
            report.append("- **Sales Recovery Plan:** Implement immediate measures to reverse declining sales")
            report.append("- **Market Analysis:** Investigate factors causing the downward trend")
            report.append("- **Customer Retention:** Focus on retaining existing customers")
            report.append("- **Promotional Activities:** Consider targeted promotions to boost sales")

        if 'bersedia' in prediction.lower() or 'willing' in prediction.lower():
            report.append("### Technology Adoption Opportunities")
            report.append("- **Digital Transformation:** Owner shows willingness to adopt new technologies")
            report.append("- **E-commerce Integration:** Consider online sales channels")
            report.append("- **POS System Upgrade:** Implement modern point-of-sale systems")
            report.append("- **Data Analytics:** Leverage business intelligence tools")

    report.append("")
    report.append("## Technical Details")
    report.append("### Models Used")
    report.append("- **ARIMA:** Time series forecasting for sales prediction")
    report.append("- **Random Forest:** Classification for business owner willingness assessment")
    report.append("- **Decision Tree:** Rule extraction for interpretable insights")

    report.append("")
    report.append("### Data Sources")
    report.append("- Business owner dataset")
    report.append("- Historical sales data")
    report.append("- Processed feature engineering data")

    report.append("")
    report.append("---")
    report.append("*Report generated automatically by Business Analysis System*")

    return "\n".join(report)

def main():
    """Main function to generate the report"""
    print("Generating Business Analysis Report...")

    # Ensure output directory exists
    os.makedirs("reports", exist_ok=True)

    # Generate the markdown report
    report_content = generate_markdown_report()

    # Save the report
    report_filename = f"reports/business_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"

    try:
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"✅ Report successfully generated: {report_filename}")
        print(f"📄 Report contains {len(report_content.split())} words")

        # Also create a latest report
        latest_filename = "reports/latest_business_analysis_report.md"
        with open(latest_filename, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"✅ Latest report saved as: {latest_filename}")

    except Exception as e:
        print(f"❌ Error saving report: {e}")

if __name__ == "__main__":
    main()
